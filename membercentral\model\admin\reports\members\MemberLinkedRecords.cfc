<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>		

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfset local.frmLeftRT = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmleftrt/text())")>
			<cfset local.frmRightRT = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrightrt/text())")>
			<cfset local.frmRightRTRT = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrightrtrt/text())")>
			<cfset local.frmRightGroupID = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrightgroupid/text())")>
			<cfset local.frmRightRTLRNum = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrightrtlrnum/text())")>

			<cfif val(local.frmRightGroupID) neq 0>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySelectedGroup">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select groupPathExpanded 
						from dbo.ams_groups 
						where groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.frmRightGroupID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>

			<cfset local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups')>
			<cfset local.qryOrgRecordTypes = application.objOrgInfo.getOrgRecordTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRoles">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select rt.recordTypeID, rtrt.recordTypeRelationshipTypeID, linkingRT.recordTypeName as linkingRecordTypeName, rrt.relationshipTypeName
					from dbo.ams_recordTypes as rt
					inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.childRecordTypeID = rt.recordTypeID
					inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
					inner join dbo.ams_recordTypes as linkingRT on rtrt.masterRecordTypeID = linkingRT.recordTypeID
					where rt.orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
					and rtrt.isActive = 1
						union all
					select rt.recordTypeID, rtrt.recordTypeRelationshipTypeID, linkingRT.recordTypeName as linkingRecordTypeName, rrt.relationshipTypeName
					from dbo.ams_recordTypes as rt
					inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.masterRecordTypeID = rt.recordTypeID
					inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
					inner join dbo.ams_recordTypes as linkingRT on rtrt.childRecordTypeID = linkingRT.recordTypeID
					where rt.orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
					and rtrt.isActive = 1
					order by rt.recordTypeID, linkingRecordTypeName, rrt.relationshipTypeName;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				<script language="javascript">
					function selGroup(fldID) {
						var selhref = '#local.grpSelectLink#&mode=direct&retFunction=top.doSelectGroup&fldName=' + fldID;
						MCModalUtils.showModal({
							isslideout: true,
							modaloptions: {
								backdrop: 'static',
								keyboard: false
							},
							size: 'xl',
							title: 'Select Group',
							iframe: true,
							contenturl: selhref,
							strmodalfooter: {
								classlist: 'd-none'
							}
						});
					}
					function doSelectGroup(fldID,gID,gPath,bypass) {
						bypass = (typeof bypass === 'undefined') ? '0' : bypass;
						$('input##frmRightGroupID').val(gID);
						var newgPath = gPath.split("\\");
							if (bypass == 0) newgPath.shift();
							newgPath = newgPath.join(" \\ ");
						$('span##spanGroupInfo').html(newgPath);
						$('span##spanGroupClear').show();
						$('span##spanGroupSelect a').html('Select Other Group');
						return false;
					}
					function clearSelGroup(fldID) {
						$('input##frmRightGroupID').val('');
						$('span##spanGroupInfo').html('');
						$('span##spanGroupClear').hide();
						$('span##spanGroupSelect a').html('Select Group');
					}
					function closeBox() { MCModalUtils.hideModal(); }

					function frmRightRTRTChange() {
						$('##divReportShowScreenLoading, div.tbodyrightrtrt').hide();
						$('##divReportShowScreen').html('').hide();
						$('div.tbodyrightrtrt select').attr('disabled',true);

						var currSelRT = $('##frmRightRT').val() || '';
						for (var i=0, tot=currSelRT.length; i < tot; i++) {
							$('div##tbodyRightRTRT'+currSelRT[i]+' select').attr('disabled',false);
							$('div##tbodyRightRTRT'+currSelRT[i]).show();
						}
					}	

					$(function() {
						mca_setupSelect2();
						frmRightRTRTChange();

						$('span##spanGroupClear').hide();
						<cfif val(local.frmRightGroupID) neq 0>
							doSelectGroup('',#local.frmRightGroupID#,'#JSStringFormat(local.qrySelectedGroup.groupPathExpanded)#',1)
						</cfif>
					});
				</script>
				<style type="text/css">
				span##spanGroupInfo { font-weight:bold; display:block; margin-bottom:4px; }
				</style>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
				
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					
					<cfform name="frmReport" id="frmReport" method="post">
					<cfinput type="hidden" name="reportAction" id="reportAction" value="">
					#showStepMemberCriteria(event=arguments.event, title="Define Left-Side Filter", desc="Filter the members appearing on the left-side of this report using the defined criteria below.")#

					<div class="mb-5 stepDIV" style="margin-top:-30px;">
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">									
									<label for="frmLeftRT" class="col-md-4 col-sm-12 col-form-label">Record Types</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmLeftRT" id="frmLeftRT" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="Select options">
											<cfloop query="local.qryOrgRecordTypes">
												<option value="#local.qryOrgRecordTypes.recordTypeID#" <cfif listFind(local.frmLeftRT,local.qryOrgRecordTypes.recordTypeID)>selected</cfif>>#local.qryOrgRecordTypes.recordTypeName#</option>
											</cfloop>
										</select>
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="mb-5 stepDIV">
						<h5>Define Right-Side Filter</h5>
						<div>Optionally, filter the members appearing on the right-side of this report using the defined criteria below.</div>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmLeftRT" class="col-md-4 col-sm-12 col-form-label">Members of</label>
									<div class="col-md-8 col-sm-12">
										<span id="spanGroupInfo"></span>
										<span id="spanGroupSelect"><a href="javascript:void(0);" onclick="selGroup('frmRightGroupID')">Select Group</a></span>
										<span id="spanGroupClear">&bull; <a href="javascript:void(0);" onclick="clearSelGroup('frmRightGroupID')">Clear Selection</a></span>
										<cfinput type="hidden" name="frmRightGroupID" id="frmRightGroupID" value="">
									</div>
								</div>
								<div class="form-group row">
									<label for="frmRightRT" class="col-md-4 col-sm-12 col-form-label">Record Types</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmRightRT" id="frmRightRT" onChange="frmRightRTRTChange()" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="Select options">
											<cfloop query="local.qryOrgRecordTypes">
												<option value="#local.qryOrgRecordTypes.recordTypeID#" <cfif listFind(local.frmRightRT,local.qryOrgRecordTypes.recordTypeID)>selected</cfif>>#local.qryOrgRecordTypes.recordTypeName#</option>
											</cfloop>
										</select>
									</div>
								</div>
								<cfloop query="local.qryOrgRecordTypes">
									<cfquery name="local.qryRolesRT" dbtype="query">
										select recordTypeRelationshipTypeID, linkingRecordTypeName + ': ' + relationshipTypeName as role
										from [local].qryRoles
										where recordTypeID = #local.qryOrgRecordTypes.recordTypeID#
										order by linkingRecordTypeName, relationshipTypeName
									</cfquery>

									<cfset local.thisSelectedRecTypeRolesList = "">
									<cfset local.thisRecTypeIndex = ListFind(local.frmRightRT, local.qryOrgRecordTypes.recordTypeID)>
									<cfif local.thisRecTypeIndex gt 0 and ListIndexExists(local.frmRightRTRT, local.thisRecTypeIndex, "|")>
										<cfset local.thisSelectedRecTypeRolesList = ListGetAt(local.frmRightRTRT, local.thisRecTypeIndex, "|")>
										<cfif local.thisSelectedRecTypeRolesList eq "ALL">
											<cfset local.thisSelectedRecTypeRolesList = "">
										</cfif>
									</cfif>

									<div class="form-group row tbodyrightrtrt" id="tbodyRightRTRT#local.qryOrgRecordTypes.recordTypeID#" style="display:none;">
										<label for="frmLeftRT" class="col-md-4 col-sm-12 col-form-label">Limit to roles of #local.qryOrgRecordTypes.recordTypeName#</label>
										<div class="col-md-8 col-sm-12">
											<select name="frmRightRTRT_#local.qryOrgRecordTypes.recordTypeID#" id="frmRightRTRT_#local.qryOrgRecordTypes.recordTypeID#" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="All roles">
												<cfloop query="local.qryRolesRT">
													<option value="#local.qryRolesRT.recordTypeRelationshipTypeID#" <cfif listFind(local.thisSelectedRecTypeRolesList, local.qryRolesRT.recordTypeRelationshipTypeID)>selected</cfif>>#local.qryRolesRT.role#</option>
												</cfloop>
											</select>
										</div>
									</div>
								</cfloop>
								<div class="form-group row">
									<label for="frmRightRTLRNum" class="col-md-4 col-sm-12 col-form-label">Minimum Number of Linked Records</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="frmRightRTLRNum" id="frmRightRTLRNum" value="#local.frmRightRTLRNum#" maxlength="10" class="form-control form-control-sm" >
									</div>
								</div>
							</div>
						</div>
					</div>

					#showStepFieldsets(event=arguments.event, title="Select Field Sets to Include in This Report for the Left-Side Member", mode=1)#
					#showStepFieldsets(event=arguments.event, title="Select Field Sets to Include in This Report for the Right-Side Member", mode=2)#
					#showButtonBar(event=arguments.event)#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="generateData" access="private" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="frmLeftRT" type="string" required="true">
		<cfargument name="frmRightRT" type="string" required="true">
		<cfargument name="frmRightRTRT" type="string" required="true">
		<cfargument name="frmRightGroupID" type="string" required="true">
		<cfargument name="frmRightRTLRNum" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfif arguments.reportAction eq "customcsv">
			<cfset local.mode = "export">
		<cfelse>
			<cfset local.mode = "report">
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strReturn.qryData" result="local.strReturn.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.RuleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
				
				IF OBJECT_ID('tempdb..##tmpReport') IS NOT NULL
					DROP TABLE ##tmpReport;
				IF OBJECT_ID('tempdb..##tmpLeftSide') IS NOT NULL
					DROP TABLE ##tmpLeftSide;
				IF OBJECT_ID('tempdb..##tmpRightSide') IS NOT NULL
					DROP TABLE ##tmpRightSide;
				IF OBJECT_ID('tempdb..##tmpReportFinal') IS NOT NULL
					DROP TABLE ##tmpReportFinal;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;
				IF OBJECT_ID('tempdb..##tmpLinkedMembersFS') IS NOT NULL
					DROP TABLE ##tmpLinkedMembersFS;
				CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);
				CREATE TABLE ##tmpLinkedMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
					@outputFieldsXML xml, @linkedMembersOutputFieldsXML xml;

				-- get left side matches
				select m.memberID, m.lastName, m.firstName, m.memberNumber, m.company, m.hasMemberPhotoThumb, leftRT.recordTypeID, leftRT.recordTypeName
				into ##tmpLeftSide
				from dbo.ams_members as m
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
				inner join dbo.ams_recordTypes as leftRT on leftRT.recordTypeID = m.recordTypeID
					<cfif listLen(arguments.frmLeftRT)>
						and leftRT.recordTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.frmLeftRT#">)
					</cfif>
				where m.activeMemberID = m.memberID
				and m.orgID = @orgID
				and m.isProtected = 0
				and m.status <> 'D';

				-- get right side matches
				select tls.memberID as leftSideMemberID, masterMember.memberID as rightSideMemberid, masterRT.recordTypeID, masterRT.recordTypeName, 
					STRING_AGG(rrt.relationshipTypeName,', ') as relationshipList
				into ##tmpRightSide
				from ##tmpLeftSide as tls
				inner join dbo.ams_members as childMember on tls.memberID = childMember.memberID and childMember.orgID = @orgID
				inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID and rr.childMemberID = childMember.memberID and rr.isActive = 1
				inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID and rtrt.isActive = 1
				inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID	
				inner join dbo.ams_members as mm on rr.masterMemberID = mm.memberID and mm.status <> 'D' and mm.orgID = @orgID
				inner join dbo.ams_members as masterMember on mm.activeMemberID = masterMember.memberID and masterMember.orgID = @orgID
					and masterMember.isProtected = 0
					and masterMember.status <> 'D'
				<cfif val(arguments.frmRightGroupID)>
					inner join dbo.cache_members_groups as cmg on cmg.orgID = @orgID
						and cmg.memberID = masterMember.memberID 
						and cmg.groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.frmRightGroupID)#">
				</cfif>
				inner join dbo.ams_recordTypes as masterRT on masterRT.recordTypeID = masterMember.recordTypeID
				<cfif listlen(arguments.frmRightRT) and NOT listlen(arguments.frmRightRTRT)>
					where masterRT.recordTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.frmRightRT#">)
				<cfelseif listlen(arguments.frmRightRT) and listlen(arguments.frmRightRTRT)>
					where (
						<cfloop list="#arguments.frmRightRT#" item="local.thisRecType" index="local.recTypeIndex">
							<cfset local.thisRecTypeRolesList = ListGetAt(arguments.frmRightRTRT, local.recTypeIndex, "|")>
							<cfif local.recTypeIndex gt 1>OR</cfif>
							(
								masterRT.recordTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRecType#">
								<cfif local.thisRecTypeRolesList neq 'ALL'>
									AND rtrt.recordTypeRelationshipTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.thisRecTypeRolesList#">)
								</cfif>
							)
						</cfloop>
					)
				</cfif>
				group by tls.memberID, masterMember.memberID, masterRT.recordTypeID, masterRT.recordTypeName
					union
				select tls.memberID as leftSideMemberID, childMember.memberID as rightSideMemberid, childRT.recordTypeID, childRT.recordTypeName, 
					STRING_AGG(rrt.relationshipTypeName,', ') as relationshipList
				from ##tmpLeftSide as tls
				inner join dbo.ams_members as masterMember on tls.memberID = masterMember.memberID and masterMember.orgID = @orgID
				inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID and rr.masterMemberID = masterMember.memberID and rr.isActive = 1
				inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID and rtrt.isActive = 1
				inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID	
				inner join dbo.ams_members as cm on rr.childMemberID = cm.memberID and cm.status <> 'D' and cm.orgID = @orgID
				inner join dbo.ams_members as childMember on cm.activeMemberID = childMember.memberID and childMember.orgID = @orgID
					and childMember.isProtected = 0
					and childMember.status <> 'D'
				<cfif val(arguments.frmRightGroupID)>
					inner join dbo.cache_members_groups as cmg on cmg.orgID = @orgID
						and cmg.memberID = childMember.memberID 
						and cmg.groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.frmRightGroupID)#">
				</cfif>
				inner join dbo.ams_recordTypes as childRT on childRT.recordTypeID = childMember.recordTypeID
				<cfif listlen(arguments.frmRightRT) and NOT listlen(arguments.frmRightRTRT)>
					where childRT.recordTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.frmRightRT#">)
				<cfelseif listlen(arguments.frmRightRT) and listlen(arguments.frmRightRTRT)>
					where (
						<cfloop list="#arguments.frmRightRT#" item="local.thisRecType" index="local.recTypeIndex">
							<cfset local.thisRecTypeRolesList = ListGetAt(arguments.frmRightRTRT, local.recTypeIndex, "|")>
							<cfif local.recTypeIndex gt 1>OR</cfif>
							(
								childRT.recordTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRecType#">
								<cfif local.thisRecTypeRolesList neq 'ALL'>
									AND rtrt.recordTypeRelationshipTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.thisRecTypeRolesList#">)
								</cfif>
							)
						</cfloop>
					)
				</cfif>
				group by tls.memberID, childMember.memberID, childRT.recordTypeID, childRT.recordTypeName;

				-- combine matches into report
				select m.memberID, mLink.rightSideMemberid as linked_memberID, m.memberNumber, m.Company, 
					<cfif arguments.reportAction eq "customcsv">
						m.lastName as [Last Name], m.firstName as [First Name], m.recordTypeName as MCRecordType,
						mLinkMem.lastName as [Linked Last Name], mLinkMem.firstName as [Linked First Name], 
						mLinkMem.memberNumber as [Linked MemberNumber], mLinkMem.company as [Linked Company], 
						mLink.recordTypeName as [Linked MCRecordType], 
					<cfelse>
						m.lastName, m.firstName, m.recordTypeName, mLinkMem.lastName as linked_LastName, mLinkMem.firstName as linked_FirstName, 
						mLinkMem.memberNumber as linked_MemberNumber, mLinkMem.company as linked_company, mLink.recordTypeName as linked_recordTypeName, 
					</cfif>
					m.hasMemberPhotoThumb, mLinkMem.hasMemberPhotoThumb as linked_hasMemberPhotoThumb, mLink.relationshipList as linked_relationshipList
				into ##tmpReport
				from ##tmpLeftSide as m
				inner join ##tmpRightSide as mLink on mLink.leftSideMemberID = m.memberID
				<cfif len(arguments.frmRightRTLRNum)>
				inner join (SELECT leftSideMemberID FROM ##tmpRightSide GROUP BY leftSideMemberID having count(leftSideMemberID) >= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.frmRightRTLRNum)#">) as clm on clm.leftSideMemberID = mLink.leftSideMemberID
				</cfif>
				inner join dbo.ams_members as mLinkMem on mLinkMem.memberID = mLink.rightSideMemberid and mLinkMem.orgID = @orgID;

				CREATE NONCLUSTERED INDEX IX_memberid ON ##tmpReport (memberID);
				CREATE NONCLUSTERED INDEX IX_linkedmemberid ON ##tmpReport (linked_memberID);
				
				-- get members fieldset data and set back to snapshot because proc ends in read committed
				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
					@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.fieldSetIDList#">,
					@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
					@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.ovNameFormat#">,
					@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strSQLPrep.ovMaskEmails#">,
					@membersTableName='##tmpReport', @membersResultTableName='##tmpMembersFS', 
					@linkedMembers=0, @mode=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.mode#">, 
					@outputFieldsXML=@outputFieldsXML OUTPUT;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				-- get linked members fieldset data and set back to snapshot because proc ends in read committed
				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
					@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.linked_fieldSetIDList#">,
					@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
					@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.linked_ovNameFormat#">,
					@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strSQLPrep.linked_ovMaskEmails#">,
					@membersTableName='##tmpReport', @membersResultTableName='##tmpLinkedMembersFS', 
					@linkedMembers=1, @mode=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.mode#">, 
					@outputFieldsXML=@linkedMembersOutputFieldsXML OUTPUT;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				<cfif arguments.reportAction eq "customcsv">
					select tm.[Last Name], tm.[First Name], tm.memberNumber as MemberNumber, tm.Company, tm.MCRecordType,
						tm.[Linked Last Name], tm.[Linked First Name], tm.[Linked MemberNumber], tm.[Linked Company], 
						tm.[Linked MCRecordType], tm.linked_relationshipList as [Linked Roles], m.*, mLink.*
					into ##tmpReportFinal
					from ##tmpReport as tm
					inner join ##tmpMembersFS AS m on tm.memberID = m.memberID
					left outer join ##tmpLinkedMembersFS AS mLink on tm.Linked_memberID = mLink.Linked_memberID;
					
					#generateFinalBCPTable(tblName="##tmpReportFinal", dropFields="memberid,Linked_memberid")#
				<cfelse>
					SELECT *, CASE WHEN rowNum = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML, 
						CASE WHEN rowNum = 1 THEN @linkedMembersOutputFieldsXML ELSE NULL END AS mc_linkedMembersOutputFieldsXML
					FROM (
						SELECT tm.lastName, tm.firstName, tm.memberNumber, tm.Company, tm.hasMemberPhotoThumb, tm.recordTypeName,
							tm.linked_LastName, tm.linked_FirstName, tm.linked_MemberNumber, tm.linked_company, tm.linked_hasMemberPhotoThumb, 
							tm.linked_recordTypeName, tm.linked_relationshipList, m.*, mLink.*, 
							ROW_NUMBER() OVER (ORDER BY tm.lastName, tm.firstName, tm.company, tm.memberNumber, tm.linked_LastName, tm.linked_FirstName, tm.linked_company, tm.linked_MemberNumber) AS rowNum
						FROM ##tmpReport AS tm
						INNER JOIN ##tmpMembersFS AS m ON tm.memberID = m.memberID
						LEFT OUTER JOIN ##tmpLinkedMembersFS AS mLink ON tm.Linked_memberID = mLink.Linked_memberID
					) AS finalData
					ORDER BY rowNum;
				</cfif>
				
				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpReport') IS NOT NULL
					DROP TABLE ##tmpReport;
				IF OBJECT_ID('tempdb..##tmpLeftSide') IS NOT NULL
					DROP TABLE ##tmpLeftSide;
				IF OBJECT_ID('tempdb..##tmpRightSide') IS NOT NULL
					DROP TABLE ##tmpRightSide;
				IF OBJECT_ID('tempdb..##tmpReportFinal') IS NOT NULL
					DROP TABLE ##tmpReportFinal;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;
				IF OBJECT_ID('tempdb..##tmpLinkedMembersFS') IS NOT NULL
					DROP TABLE ##tmpLinkedMembersFS;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(local.mc_siteInfo.orgcode) & "/memberphotosth/">

			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m,mLink")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfif arguments.reportAction eq "pdf">
				<cfset local.isPDF = true>
			<cfelse>
				<cfset local.isPDF = false>
			</cfif>
			
			<!--- get extra fields --->
			<cfset local.frmLeftRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmleftrt/text())")>
			<cfset local.frmRightRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrightrt/text())")>
			<cfset local.frmRightRTRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrightrtrt/text())")>
			<cfset local.frmRightGroupID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrightgroupid/text())")>
			<cfset local.frmRightRTLRNum = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrightrtlrnum/text())")>
			<cfset local.frmLeftShowPhotos = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@img)")>
			<cfset local.frmLeftShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mn)")>
			<cfset local.frmLeftShowMemberCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mc)")>
			<cfset local.frmRightShowPhotos = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/linkedfieldsets/@img)")>
			<cfset local.frmRightShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/linkedfieldsets/@mn)")>
			<cfset local.frmRightShowMemberCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/linkedfieldsets/@mc)")>

			<cfset local.strReport = generateData(orgID=local.mc_siteInfo.orgID, strSQLPrep=local.strSQLPrep, reportAction='screen', frmLeftRT=local.frmLeftRT, 
				frmRightRT=local.frmRightRT, frmRightRTRT=local.frmRightRTRT, frmRightRTLRNum=local.frmRightRTLRNum, frmRightGroupID=local.frmRightGroupID)>
		
			<cfif local.strReport.qryData.recordcount>
				<!--- remove fields from qryOutputFields that are handled manually --->
				<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=local.strReport.qryData.mc_outputFieldsXML)>
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					select *
					from [local].qryOutputFields
					where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
				</cfquery>

				<cfset local.qryLinkedMemberOutputFields = getOutputFieldsFromXML(outputFieldsXML=local.strReport.qryData.mc_linkedMembersOutputFieldsXML)>
				<cfquery name="local.qryOutputFieldsForLoopLinked" dbtype="query">
					select *
					from [local].qryLinkedMemberOutputFields
					where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
				</cfquery>
			</cfif>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>

				<cfif local.strReport.qryData.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfoutput>
					<table class="table table-sm table-borderless">
					<thead>
						<tr>
							<th class="text-left">Linked Records</th>
						</tr>
					</thead>
					</table>
					</cfoutput>
					<cfoutput query="local.strReport.qryData" group="memberID">
						<div class="py-2">
							<table class="table table-sm table-borderless mb-1">
							<tr>
								<cfif local.frmLeftShowPhotos is 1>
									<td class="align-top" style="width:80px;">
										<cfif local.strReport.qryData.hasMemberPhotoThumb is 1>
											<cfif local.isPDF is 0>
												<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.strReport.qryData.MemberNumber)#.jpg">
											<cfelse>
												<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.strReport.qryData.MemberNumber)#.jpg">
											</cfif>
										<cfelse>
											<cfif local.isPDF is 0>
												<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
											<cfelse>
												<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
											</cfif>
										</cfif>
									</td>
								</cfif>
								<td class="align-top">
									<cfif local.isPDF is 0>
										<a href="#local.memberLink#&memberid=#local.strReport.qryData.memberid#" target="_blank"><cfif local.frmLeftShowMemberNumber is 1>#local.strReport.qryData["Extended MemberNumber"]#<cfelse>#local.strReport.qryData["Extended Name"]#</cfif></a><br/>
									<cfelse>
										<b><cfif local.frmLeftShowMemberNumber is 1>#local.strReport.qryData["Extended MemberNumber"]#<cfelse>#local.strReport.qryData["Extended Name"]#</cfif></b><br/>
									</cfif>
									<cfif local.frmLeftShowMemberCompany is 1 AND len(local.strReport.qryData.company)>#local.strReport.qryData.company#<br/></cfif>
									<cfloop query="local.qryOutputFields">
										<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
											#local.qryOutputFields.fieldlabel#: #local.strReport.qryData[local.qryOutputFields.dbfield][local.strReport.qryData.currentrow]#<br/>
										</cfif>
									</cfloop>
									<cfloop query="local.qryOutputFieldsForLoop">
										<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
											#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryData.currentrow])#<br/>
										<cfelseif len(local.strReport.qryData[local.qryOutputFieldsForLoop.fieldLabel][local.strReport.qryData.currentrow])>
											<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
												#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryData.currentrow], "m/d/yyyy")#<br/>
											<cfelse>
												#local.qryOutputFieldsForLoop.fieldlabel#: #local.strReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryData.currentrow]#<br/>
											</cfif>
										</cfif>
									</cfloop>
								</td>
							</tr>
							</table>
						</div>

						<table class="table table-sm table-borderless mb-1">
						<cfoutput>
							<tr>
								<td style="width:30px;">&nbsp;</td>
								<cfif local.frmRightShowPhotos is 1>
									<td class="align-top" style="width:80px;">
										<cfif local.strReport.qryData.linked_hasMemberPhotoThumb is 1>
											<cfif local.isPDF is 0>
												<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.strReport.qryData.linked_MemberNumber)#.jpg">
											<cfelse>
												<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.strReport.qryData.linked_MemberNumber)#.jpg">
											</cfif>
										<cfelse>
											<cfif local.isPDF is 0>
												<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
											<cfelse>
												<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
											</cfif>
										</cfif>
									</td>
								</cfif>
								<td class="align-top">
									<cfif local.isPDF is 0>
										<a href="#local.memberLink#&memberid=#local.strReport.qryData.LINKED_memberid#" target="_blank"><cfif local.frmRightShowMemberNumber is 1>#local.strReport.qryData["Linked Extended MemberNumber"]#<cfelse>#local.strReport.qryData["Linked Extended Name"]#</cfif></a><br/>
									<cfelse>
										<b><cfif local.frmRightShowMemberNumber is 1>#local.strReport.qryData["Linked Extended MemberNumber"]#<cfelse>#local.strReport.qryData["Linked Extended Name"]#</cfif></b><br/>
									</cfif>
									<cfif local.frmRightShowMemberCompany is 1 AND len(local.strReport.qryData.linked_company)>#local.strReport.qryData.linked_company#<br/></cfif>								
									<cfloop query="local.qryLinkedMemberOutputFields">
										<cfif left(local.qryLinkedMemberOutputFields.fieldCode,25) eq "linked_mc_combinedAddress">
											#local.qryLinkedMemberOutputFields.fieldlabel#: #local.strReport.qryData[local.qryLinkedMemberOutputFields.dbfield][local.strReport.qryData.currentrow]#<br/>
										</cfif>
									</cfloop>
									<cfloop query="local.qryOutputFieldsForLoopLinked">
										<cfif left(local.qryOutputFieldsForLoopLinked.dbField,13) eq "acct_balance_">
											#local.qryOutputFieldsForLoopLinked.fieldlabel#: #DollarFormat(local.strReport.qryData[local.qryOutputFieldsForLoopLinked.fieldlabel][local.strReport.qryData.currentrow])#<br/>
										<cfelseif len(local.strReport.qryData[local.qryOutputFieldsForLoopLinked.fieldLabel][local.strReport.qryData.currentrow])>
											<cfif local.qryOutputFieldsForLoopLinked.dataTypeCode eq "DATE">
												#local.qryOutputFieldsForLoopLinked.fieldlabel#: #DateFormat(local.strReport.qryData[local.qryOutputFieldsForLoopLinked.fieldlabel][local.strReport.qryData.currentrow], "m/d/yyyy")#<br/>
											<cfelse>
												#local.qryOutputFieldsForLoopLinked.fieldlabel#: #local.strReport.qryData[local.qryOutputFieldsForLoopLinked.fieldlabel][local.strReport.qryData.currentrow]#<br/>
											</cfif>
										</cfif>
									</cfloop>
								</td>
								<td class="align-top text-right" style="width:300px;">
									#local.strReport.qryData.linked_relationshipList#<br/>
								</td>
							</tr>
						</cfoutput>
						</table>
					</cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.strReport.qryData", strQryResult=local.strReport.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>
			
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m,mLink")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>
	
			<!--- get extra fields --->
			<cfset local.frmLeftRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmleftrt/text())")>
			<cfset local.frmRightRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrightrt/text())")>
			<cfset local.frmRightRTRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrightrtrt/text())")>
			<cfset local.frmRightGroupID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrightgroupid/text())")>
			<cfset local.frmRightRTLRNum = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrightrtlrnum/text())")>
			<cfscript>
			local.strReport = generateData(orgID=local.mc_siteInfo.orgID, strSQLPrep=local.strSQLPrep, reportAction='customcsv', frmLeftRT=local.frmLeftRT, 
				frmRightRT=local.frmRightRT, frmRightRTRT=local.frmRightRTRT, frmRightRTLRNum=local.frmRightRTLRNum,frmRightGroupID=local.frmRightGroupID);

			local.arrInitialReportSort = arrayNew(1);
			local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='MCRecordType', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='Linked MCRecordType', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='Linked Roles', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strReportQry = { qryReportFields=local.strReport.qryData, strQryResult=local.strReport.qryDataResult };
			local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML);
			</cfscript>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		
		local.strFields = structNew();
		local.strFields.frmleftrt = { label="Record Types (Left-Side Filter)", value=arguments.event.getValue('frmLeftRT','') };
		local.strFields.frmrightrt = { label="Record Types (Right-Side Filter)", value=arguments.event.getValue('frmRightRT','') };
		local.strFields.frmrightgroupid = { label="Members of Group (Right-Side Filter)", value=arguments.event.getValue('frmRightGroupID','') };
		local.strFields.frmincphotos = { label="Include Photos", value=arguments.event.getValue('frmIncPhotos','') };
		local.strFields.frmrightrtlrnum = { label="Minimum Number of Linked Records", value=arguments.event.getValue('frmRightRTLRNum','') };
		</cfscript>
		
		<cfset local.recordTypeRoles = structNew()>
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisEl">
			<cfif left(local.thisEl,13) eq "frmRightRTRT_">
				<cfif not structKeyExists(local.recordTypeRoles,GetToken(local.thisEl,2,'_'))>
					<cfset structInsert(local.recordTypeRoles, GetToken(local.thisEl,2,'_'), arguments.event.getValue(local.thisEl))>
				</cfif>
			</cfif>
		</cfloop>

		<cfset local.strFields.frmrightrtrt = { label="Roles of Record Type", value="" }>
		<cfif NOT StructIsEmpty(local.recordTypeRoles)>
			<cfloop list="#local.strFields.frmrightrt.value#" item="local.thisRecType">
				<cfif StructKeyExists(local.recordTypeRoles, local.thisRecType)>
					<cfset local.strFields.frmrightrtrt.value = listAppend(local.strFields.frmrightrtrt.value, local.recordTypeRoles[local.thisRecType], "|")>
				<cfelse>
					<cfset local.strFields.frmrightrtrt.value = listAppend(local.strFields.frmrightrtrt.value, "ALL", "|")>
				</cfif>
			</cfloop>
		</cfif>

		<cfset reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event)>
		<cfreturn returnAppStruct('','echo')>
	</cffunction>

</cfcomponent>